import React from "react"
// Card components removed - using plain divs for transparent design
import { But<PERSON> } from "@/components/ui/button"
import { BookOpen } from "lucide-react"

import { useAppTranslation } from "@src/i18n/TranslationContext"
import { vscode } from "@src/utils/vscode"
import { useAuth } from "@src/context/AuthContext"

export const AuthenticationRequired = () => {
	const { t } = useAppTranslation()
	const { isAuthenticating, authError, clearError, signIn } = useAuth()

	// Get the logo URI from window global (same as AccountView)
	const logoUri = (window as any).IMAGES_BASE_URI + "/cubent-logo.svg"

	const handleSignIn = () => {
		clearError() // Clear any previous errors
		signIn() // Update local state
		vscode.postMessage({ type: "deviceOAuthSignIn" })
	}

	const handleHelpClick = () => {
		vscode.postMessage({
			type: "openExternal",
			url: "https://docs.cubent.dev",
		})
	}

	return (
		<div className="flex flex-col items-center justify-center min-h-screen p-8 bg-vscode-sideBar-background">
			<div className="w-full max-w-md space-y-8">
				{/* Logo */}
				<div className="flex justify-center">
					<div
						style={{
							backgroundColor: "var(--vscode-foreground)",
							WebkitMaskImage: `url('${logoUri}')`,
							WebkitMaskRepeat: "no-repeat",
							WebkitMaskSize: "contain",
							maskImage: `url('${logoUri}')`,
							maskRepeat: "no-repeat",
							maskSize: "contain",
						}}
						className="w-16 h-16">
						<img src={logoUri} alt="cubent coder logo" className="w-16 h-16 opacity-0" />
					</div>
				</div>

				{/* Welcome message */}
				<div className="space-y-3 max-w-[500px] mx-auto pl-4">
					<h2 className="text-xl font-medium text-vscode-foreground text-left">
						Welcome to Cubent.Dev
					</h2>
					<p className="text-vscode-descriptionForeground text-sm leading-relaxed text-left">
						Sign in to Cubent Coder to securely access AI models and your personalized settings.
					</p>
				</div>

				<div className="space-y-6 pl-4">
					{/* Error Message */}
					{authError && (
						<div className="p-3 bg-red-900/20 border border-red-800 rounded-lg">
							<p className="text-red-400 text-sm text-center">{authError}</p>
						</div>
					)}

					{/* Sign in button matching the workspace button style */}
					<button
						onClick={handleSignIn}
						disabled={isAuthenticating}
						className="flex items-center gap-2 px-3 py-2 border border-vscode-panel-border hover:border-vscode-focusBorder text-vscode-foreground rounded transition-colors text-left">
						<span className="codicon codicon-sign-in"></span>
						<span>{isAuthenticating ? "Signing in..." : "Sign in"}</span>
					</button>

					{/* Help link - left aligned like other stuff */}
					<div className="text-left">
						<button
							onClick={handleHelpClick}
							className="text-sm text-vscode-descriptionForeground hover:text-vscode-foreground underline">
							Need help? Read docs
						</button>
					</div>
				</div>
			</div>
		</div>
	)
}
